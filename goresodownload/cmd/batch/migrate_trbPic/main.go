/*
###
Description:    Batch migrate downloaded property images to new directory structure

Usage:         ./start.sh -n migrate_trbPic -d "goresodownload" -cmd "cmd/batch/migrate_trbPic/main.go -board=TRB -dir=/1200 -dryrun"

Create date:    2025-07-28
Author:         Assistant
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	goconfig "github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for file path generation")
	dirFlag    = flag.String("dir", "", "Target directory to migrate (e.g., /1200/abc12)")
	speedMeter *gospeedmeter.SpeedMeter
	dirStore   *levelStore.DirKeyStore
	queue      *goresodownload.ResourceDownloadQueue
	startTime  = time.Now()
)

// MigrationStats holds statistics for the migration process
type MigrationStats struct {
	TotalProcessed   int64
	NeedsMigration   int64
	AlreadyCorrect   int64
	MigrationSuccess int64
	MigrationFailed  int64
	DatabaseUpdated  int64
	DatabaseFailed   int64
	FilesNotFound    int64
	FilesDeleted     int64
	AddedToQueue     int64
	FieldsUnset      int64
}

// DocInfo holds document file information with hash and extension
type DocInfo struct {
	Hash      int32  // The int32 hash derived from filename
	Extension string // The file extension (e.g., ".pdf", ".mp3")
}

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

	// Initialize dirStore for directory statistics
	collection := gomongo.Coll("rni", "file_server")
	var err error
	dirStore, err = levelStore.NewDirKeyStore(*boardFlag, collection, "")
	if err != nil {
		golog.Fatalf("Failed to create dirKeyStore: %v", err)
	}
	if dirStore == nil {
		golog.Fatalf("dirKeyStore is nil")
	}

	// Initialize download queue
	queueCol := gomongo.Coll("rni", "reso_photo_download_queue")
	queue, err = goresodownload.NewResourceDownloadQueue(queueCol)
	if err != nil {
		golog.Fatalf("Failed to create download queue: %v", err)
	}
}

func main() {
	// Create context with timeout to prevent hanging
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Minute)
	defer cancel()

	golog.Info("Starting migrate_trbPic batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("migrate_trbPic batch process completed successfully")
}

func processProperties(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()

	// Validate required parameters
	if *dirFlag == "" {
		return fmt.Errorf("dir parameter is required (e.g., -dir=/1200/abc12)")
	}

	// Normalize directory path (ensure it starts with /)
	if !strings.HasPrefix(*dirFlag, "/") {
		*dirFlag = "/" + *dirFlag
	}

	// Determine query type based on directory format
	var queryType string
	if strings.HasSuffix(*dirFlag, "/") || strings.Count(*dirFlag, "/") == 1 {
		queryType = "prefix match (regex)"
	} else {
		queryType = "exact match"
	}

	golog.Info("Starting migrate_trbPic batch processing",
		"dryrun", *dryrunFlag,
		"board", *boardFlag,
		"dir", *dirFlag,
		"queryType", queryType)

	// Validate board flag
	collectionName, exists := goresodownload.BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", *boardFlag)
	}

	// Get storage paths for file operations
	storagePaths, err := levelStore.GetImageDir(*boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get image directories: %w", err)
	}
	if len(storagePaths) == 0 {
		return fmt.Errorf("no image directories configured for board %s", *boardFlag)
	}

	// Get collection using BoardMergedTable
	coll := gomongo.Coll("rni", collectionName)
	golog.Info("Processing collection",
		"board", *boardFlag,
		"collection", collectionName,
		"dryrun", *dryrunFlag,
		"storagePaths", storagePaths)

	// Query documents with the specified phoP directory
	// Support both exact match and prefix match (regex)
	var query bson.M
	if strings.HasSuffix(*dirFlag, "/") || strings.Count(*dirFlag, "/") == 1 {
		// If directory ends with "/" or only has one "/" (L1 level), use regex for prefix match
		// Example: "/1200" should match "/1200/abc12", "/1200/def34", etc.
		regexPattern := "^" + strings.TrimSuffix(*dirFlag, "/") + "/"
		query = bson.M{
			"phoP": bson.M{"$regex": regexPattern},
		}
		golog.Info("Using regex pattern for directory matching", "pattern", regexPattern)
	} else {
		// Exact match for full paths like "/1200/abc12"
		query = bson.M{
			"phoP": *dirFlag,
		}
		golog.Info("Using exact match for directory", "dir", *dirFlag)
	}

	// 根据board类型确定需要查询的字段
	projection := bson.D{
		{Key: "_id", Value: 1},
		{Key: "ts", Value: 1},
		{Key: "ListingKey", Value: 1},
		{Key: "ListingId", Value: 1},
		{Key: "phoP", Value: 1},
		{Key: "phoLH", Value: 1},
		{Key: "tnLH", Value: 1},
		{Key: "docLH", Value: 1},
		{Key: "ListingContractDate", Value: 1},
		{Key: "ModificationTimestamp", Value: 1},
		{Key: "OriginalEntryTimestamp", Value: 1},
		{Key: "BridgeModificationTimestamp", Value: 1},
		// CalculatePriority 需要的字段
		{Key: "MlsStatus", Value: 1},
		{Key: "CountyOrParish", Value: 1},
		{Key: "PropertyType", Value: 1},
		{Key: "StateOrProvince", Value: 1},
		{Key: "DaysOnMarket", Value: 1},
	}

	options := gomongo.QueryOptions{
		Projection: projection,
	}

	// Get cursor
	golog.Info("Executing query", "query", query)
	cursor, err := coll.Find(ctx, query, options)
	if err != nil {
		golog.Error("Failed to execute query", "error", err)
		return fmt.Errorf("failed to execute query: %v", err)
	}
	golog.Info("Query executed successfully, starting streaming")

	// Initialize migration stats
	stats := &MigrationStats{}

	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, coll, item, storagePaths, stats)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			printFinalStats(stats, duration)
			if err != nil {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:    10,
		Verbose: 2,
	}

	golog.Info("Starting gostreaming.Streaming")
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
		return err
	}
	golog.Info("Streaming completed successfully")
	return nil
}

// processItem processes a single document to migrate images if needed
func processItem(ctx context.Context, coll *gomongo.MongoCollection, item interface{}, storagePaths []string, stats *MigrationStats) error {
	golog.Debug("Processing item started")

	// Track processing speed
	speedMeter.Check("processed", 1)
	stats.TotalProcessed++

	// Convert item to bson.M (handle both bson.M and bson.D types)
	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item)
		speedMeter.Check("errors", 1)
		return fmt.Errorf("unsupported document type: %T", item)
	}

	golog.Debug("Item converted to bson.M successfully")

	// Extract required fields
	id, ok := doc["_id"].(string)
	if !ok {
		golog.Error("Failed to extract _id", "doc", doc)
		speedMeter.Check("idErrors", 1)
		return fmt.Errorf("failed to extract _id")
	}

	// 根据board类型确定使用哪个字段作为listingKey
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		golog.Error("Failed to get listing key", "error", err, "_id", id)
		speedMeter.Check("listingKeyErrors", 1)
		return err
	}

	currentPhoP, ok := doc["phoP"].(string)
	if !ok {
		golog.Error("Failed to extract phoP", "doc", doc, "_id", id)
		speedMeter.Check("phoPErrors", 1)
		return fmt.Errorf("failed to extract phoP")
	}

	// 使用新的PropTs获取逻辑
	ts, err := goresodownload.GetPropTsForPath(doc, *boardFlag)
	if err != nil {
		golog.Error("Failed to get PropTs for path",
			"_id", id, "board", *boardFlag, "error", err)
		speedMeter.Check("tsErrors", 1)
		return fmt.Errorf("failed to get PropTs for path: %w", err)
	}

	// Get new file path using levelStore
	newPhoP, err := levelStore.GetFullFilePathForProp(ts, *boardFlag, listingKey)
	if err != nil {
		golog.Error("Failed to get full file path",
			"_id", id,
			"listingKey", listingKey,
			"ts", ts,
			"error", err)
		speedMeter.Check("filePathErrors", 1)
		return fmt.Errorf("failed to get full file path: %w", err)
	}

	// Check if migration is needed
	if currentPhoP == newPhoP {
		golog.Debug("Path already correct, no migration needed",
			"_id", id,
			"path", currentPhoP)
		stats.AlreadyCorrect++
		speedMeter.Check("alreadyCorrect", 1)
		return nil
	}

	golog.Info("Migration needed",
		"_id", id,
		"listingKey", listingKey,
		"currentPath", currentPhoP,
		"newPath", newPhoP)
	stats.NeedsMigration++

	// Check if this is a dry run
	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		golog.Info("Dry run mode: Would migrate images",
			"_id", id,
			"listingKey", listingKey,
			"from", currentPhoP,
			"to", newPhoP)
		return nil
	}

	// Perform actual migration
	renameResults, err := migrateImages(ctx, coll, doc, currentPhoP, newPhoP, storagePaths, stats)
	if err != nil {
		golog.Error("Failed to migrate images",
			"_id", id,
			"from", currentPhoP,
			"to", newPhoP,
			"error", err)
		stats.MigrationFailed++
		speedMeter.Check("migrationFailed", 1)
		// 图片迁移失败时不更新数据库，直接返回错误
		return fmt.Errorf("image migration failed, database not updated: %w", err)
	}

	// 图片迁移成功，记录成功统计
	stats.MigrationSuccess++
	speedMeter.Check("migrationSuccess", 1)

	// 只有在图片迁移成功时才更新数据库记录
	update := bson.M{
		"$set": bson.M{
			"phoP": newPhoP,
		},
	}

	result, err := coll.UpdateOne(ctx, bson.M{"_id": id}, update)
	if err != nil {
		golog.Error("Failed to update document, rolling back image migration",
			"_id", id,
			"newPath", newPhoP,
			"error", err)

		// 数据库更新失败时，回滚图片迁移（删除硬链接）
		rollbackImageMigration(renameResults)

		stats.DatabaseFailed++
		speedMeter.Check("updateErrors", 1)
		return fmt.Errorf("failed to update document, image migration rolled back: %w", err)
	}

	// 数据库更新成功后，删除源文件
	if err := deleteSourceFiles(renameResults, stats); err != nil {
		golog.Warn("Failed to delete source files after successful database update",
			"_id", id,
			"error", err)
		// 不返回错误，因为数据库已经更新成功，迁移基本完成
	}

	// Track successful database updates
	stats.DatabaseUpdated++
	speedMeter.Check("updated", 1)

	golog.Info("Successfully processed document",
		"_id", id,
		"listingKey", listingKey,
		"from", currentPhoP,
		"to", newPhoP,
		"migrationSuccess", true,
		"modifiedCount", result.ModifiedCount)

	return nil
}

// RenameResult tracks the result of migrating a single image file using hard link strategy
type RenameResult struct {
	OldPath string
	NewPath string
	Success bool
}

// migrateImages migrates image files from old directory to new directory using hard link strategy
// Creates hard links to new location (source files will be deleted later after database update)
// Returns a list of successfully migrated files for potential rollback and any error encountered
// If files are missing, it will unset photo fields and add property to download queue
func migrateImages(ctx context.Context, coll *gomongo.MongoCollection, doc bson.M, oldPath, newPath string, storagePaths []string, stats *MigrationStats) ([]RenameResult, error) {
	// Extract phoLH (photo list hash) to get the list of image files
	phoLH, ok := doc["phoLH"]
	if !ok || phoLH == nil {
		golog.Debug("No phoLH found, skipping file migration", "oldPath", oldPath, "newPath", newPath)
		return nil, nil // No images to migrate
	}

	// Convert phoLH array to slice of base62 strings
	var imageHashes []string

	// Helper function to process a single int32 hash
	processInt32Hash := func(hashInt32 int32) (string, error) {
		base62Hash, err := levelStore.Int32ToBase62(hashInt32)
		golog.Info("Converted hash to base62", "hash", hashInt32, "base62", base62Hash)
		if err != nil {
			golog.Error("Failed to convert hash to base62", "hash", hashInt32, "error", err)
			return "", err
		}
		return base62Hash, nil
	}

	// Convert different array types to []int32 and process
	var int32Hashes []int32
	switch phoLHArray := phoLH.(type) {
	case []interface{}:
		// Handle []interface{} type
		for _, hash := range phoLHArray {
			if hashInt32, ok := hash.(int32); ok {
				int32Hashes = append(int32Hashes, hashInt32)
			} else {
				golog.Warn("Expected int32 hash in phoLH", "type", fmt.Sprintf("%T", hash), "value", hash)
			}
		}
	case primitive.A:
		// Handle BSON array type (primitive.A)
		for _, hash := range phoLHArray {
			if hashInt32, ok := hash.(int32); ok {
				int32Hashes = append(int32Hashes, hashInt32)
			} else {
				golog.Warn("Expected int32 hash in phoLH", "type", fmt.Sprintf("%T", hash), "value", hash)
			}
		}
	case []int32:
		// Handle []int32 type directly
		int32Hashes = phoLHArray
	default:
		return nil, fmt.Errorf("phoLH is not a supported array type, got type: %T", phoLH)
	}

	// Process all int32 hashes with unified logic
	for _, hashInt32 := range int32Hashes {
		base62Hash, err := processInt32Hash(hashInt32)
		if err != nil {
			continue // Error already logged in processInt32Hash
		}
		imageHashes = append(imageHashes, base62Hash)
	}

	// Handle tnLH (thumbnail hash) - assumed to be int32
	tnLH, ok := doc["tnLH"]
	if ok && tnLH != nil {
		if tnHashInt32, ok := tnLH.(int32); ok {
			base62Hash, err := levelStore.Int32ToBase62(tnHashInt32)
			if err != nil {
				golog.Error("Failed to convert tnLH to base62", "hash", tnHashInt32, "error", err)
			} else {
				imageHashes = append(imageHashes, base62Hash)
			}
		} else {
			golog.Warn("Expected int32 for tnLH", "type", fmt.Sprintf("%T", tnLH), "value", tnLH)
		}
	}

	// Handle docLH (document list hash) - process document files
	var docInfos []DocInfo
	if docLH, ok := doc["docLH"]; ok && docLH != nil {
		parsedDocInfos, err := processDocLH(docLH)
		if err != nil {
			golog.Error("Failed to process docLH", "error", err)
			return nil, fmt.Errorf("failed to process docLH: %w", err)
		}
		docInfos = parsedDocInfos
		golog.Info("Processed docLH", "documentCount", len(docInfos))
	}

	// Check if there are any files to migrate
	totalFiles := len(imageHashes) + len(docInfos)
	if totalFiles == 0 {
		golog.Debug("No files found for migration", "oldPath", oldPath, "newPath", newPath)
		return nil, nil
	}

	// 根据board类型确定使用哪个字段作为listingKey
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return nil, fmt.Errorf("failed to get listing key for file migration: %w", err)
	}

	golog.Info("Starting file migration with hard link strategy (source files will be deleted after database update)",
		"oldPath", oldPath,
		"newPath", newPath,
		"imageCount", len(imageHashes),
		"documentCount", len(docInfos),
		"totalFiles", totalFiles,
		"listingKey", listingKey)

	// Track hard link results for potential rollback
	var allRenameResults []RenameResult
	var migrationErrors []string
	migratedCount := 0

	// Phase 1: Rename all images
	for _, storagePath := range storagePaths {
		oldDir := filepath.Join(storagePath, strings.TrimPrefix(oldPath, "/"))
		newDir := filepath.Join(storagePath, strings.TrimPrefix(newPath, "/"))

		// Create new directory if it doesn't exist
		if err := os.MkdirAll(newDir, 0755); err != nil {
			migrationErrors = append(migrationErrors, fmt.Sprintf("failed to create directory %s: %v", newDir, err))
			break // Stop processing if directory creation fails
		}

		// Rename each image file
		for _, hash := range imageHashes {
			// Generate filename: ListingKey_hash.jpg
			filename := fmt.Sprintf("%s_%s.jpg", listingKey, hash)
			oldFilePath := filepath.Join(oldDir, filename)
			newFilePath := filepath.Join(newDir, filename)

			renameResult := RenameResult{
				OldPath: oldFilePath,
				NewPath: newFilePath,
				Success: false,
			}

			// Check if source file exists
			if _, err := os.Stat(oldFilePath); os.IsNotExist(err) {
				golog.Error("Source file not found - database inconsistency detected",
					"file", oldFilePath,
					"listingKey", listingKey,
					"hash", hash)

				// Handle missing files: unset photo fields and add to download queue
				if err := handleMissingFiles(ctx, coll, doc, stats); err != nil {
					golog.Error("Failed to handle missing files", "error", err, "listingKey", listingKey)
					migrationErrors = append(migrationErrors, fmt.Sprintf("failed to handle missing files: %v", err))
				}

				migrationErrors = append(migrationErrors, fmt.Sprintf("source file not found: %s", oldFilePath))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on file not found error
			}

			// Check if destination file already exists
			if _, err := os.Stat(newFilePath); err == nil {
				golog.Debug("Destination file already exists, treating as successful (source file will be deleted after database update)", "file", newFilePath)
				renameResult.Success = true
				allRenameResults = append(allRenameResults, renameResult)
				migratedCount++
				continue
			}

			// Create hard link (source file will be deleted later after database update)
			if err := os.Link(oldFilePath, newFilePath); err != nil {
				migrationErrors = append(migrationErrors, fmt.Sprintf("failed to create hard link from %s to %s: %v", oldFilePath, newFilePath, err))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on first failure
			}

			// Post-check: Verify that hard link was created successfully and file is accessible
			if _, err := os.Stat(newFilePath); err != nil {
				// Hard link creation appeared to succeed but file is not accessible
				// Clean up the failed hard link
				if removeErr := os.Remove(newFilePath); removeErr != nil {
					golog.Error("Failed to cleanup failed hard link",
						"hardLink", newFilePath, "cleanupError", removeErr)
				}
				migrationErrors = append(migrationErrors, fmt.Sprintf("hard link created but destination file not accessible: %s, error: %v", newFilePath, err))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on first failure
			}

			renameResult.Success = true
			allRenameResults = append(allRenameResults, renameResult)
			migratedCount++
			golog.Debug("Successfully created hard link (source file will be deleted after database update)", "from", oldFilePath, "to", newFilePath)
		}

		// Migrate document files if any exist
		for _, docInfo := range docInfos {
			// Convert int32 hash to base62 for filename
			base62Hash, err := levelStore.Int32ToBase62(docInfo.Hash)
			if err != nil {
				golog.Error("Failed to convert document hash to base62", "hash", docInfo.Hash, "error", err)
				migrationErrors = append(migrationErrors, fmt.Sprintf("failed to convert document hash %d to base62: %v", docInfo.Hash, err))
				break
			}

			// Generate filename: ListingKey_base62Hash.extension
			filename := fmt.Sprintf("%s_%s%s", listingKey, base62Hash, docInfo.Extension)
			oldFilePath := filepath.Join(oldDir, filename)
			newFilePath := filepath.Join(newDir, filename)

			renameResult := RenameResult{
				OldPath: oldFilePath,
				NewPath: newFilePath,
				Success: false,
			}

			// Check if source file exists
			if _, err := os.Stat(oldFilePath); os.IsNotExist(err) {
				golog.Error("Source document file not found - database inconsistency detected",
					"file", oldFilePath,
					"listingKey", listingKey,
					"hash", docInfo.Hash,
					"extension", docInfo.Extension)

				// Handle missing files: unset document fields and add to download queue
				if err := handleMissingFiles(ctx, coll, doc, stats); err != nil {
					golog.Error("Failed to handle missing document files", "error", err, "listingKey", listingKey)
					migrationErrors = append(migrationErrors, fmt.Sprintf("failed to handle missing document files: %v", err))
				}

				migrationErrors = append(migrationErrors, fmt.Sprintf("source document file not found: %s", oldFilePath))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on file not found error
			}

			// Check if destination file already exists
			if _, err := os.Stat(newFilePath); err == nil {
				golog.Debug("Destination document file already exists, treating as successful (source file will be deleted after database update)", "file", newFilePath)
				renameResult.Success = true
				allRenameResults = append(allRenameResults, renameResult)
				migratedCount++
				continue
			}

			// Create hard link (source file will be deleted later after database update)
			if err := os.Link(oldFilePath, newFilePath); err != nil {
				migrationErrors = append(migrationErrors, fmt.Sprintf("failed to create hard link for document from %s to %s: %v", oldFilePath, newFilePath, err))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on first failure
			}

			// Post-check: Verify that hard link was created successfully and file is accessible
			if _, err := os.Stat(newFilePath); err != nil {
				// Hard link creation appeared to succeed but file is not accessible
				// Clean up the failed hard link
				if removeErr := os.Remove(newFilePath); removeErr != nil {
					golog.Error("Failed to cleanup failed document hard link",
						"hardLink", newFilePath, "cleanupError", removeErr)
				}
				migrationErrors = append(migrationErrors, fmt.Sprintf("document hard link created but destination file not accessible: %s, error: %v", newFilePath, err))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on first failure
			}

			renameResult.Success = true
			allRenameResults = append(allRenameResults, renameResult)
			migratedCount++
			golog.Debug("Successfully created document hard link (source file will be deleted after database update)", "from", oldFilePath, "to", newFilePath)
		}

		// If there were errors in current storage path, stop processing other storage paths
		if len(migrationErrors) > 0 {
			break
		}
	}

	// If migration phase failed, rollback successfully migrated files and return error
	if len(migrationErrors) > 0 {
		golog.Warn("Hard link migration phase failed, rolling back successfully migrated files",
			"errorCount", len(migrationErrors),
			"migrationResultsCount", len(allRenameResults))

		// Rollback successfully migrated files
		rollbackImageMigration(allRenameResults)
		return nil, fmt.Errorf("hard link migration errors: %s", strings.Join(migrationErrors, "; "))
	}

	golog.Info("Hard link creation completed successfully (source files will be deleted after database update)",
		"oldPath", oldPath,
		"newPath", newPath,
		"totalImages", len(imageHashes),
		"totalDocuments", len(docInfos),
		"totalFiles", totalFiles,
		"migratedCount", migratedCount,
		"strategy", "hard link")

	// Update dirStore statistics after successful migration
	if !*dryrunFlag {
		if err := updateDirStoreStats(oldPath, newPath, migratedCount); err != nil {
			golog.Warn("Failed to update dirStore stats", "error", err)
			// Don't return error as migration was successful
		}
	}

	return allRenameResults, nil
}

// getListingKey extracts the listing key from the document based on the board type.
func getListingKey(doc bson.M, board string) (string, error) {
	var listingKey string
	var ok bool

	id, _ := doc["_id"].(string) // For logging purposes

	if board == "TRB" {
		listingKey, ok = doc["ListingKey"].(string)
		if !ok {
			return "", fmt.Errorf("failed to extract ListingKey for TRB board, _id: %s", id)
		}
	} else {
		listingKey, ok = doc["ListingId"].(string)
		if !ok {
			return "", fmt.Errorf("failed to extract ListingId for board %s, _id: %s", board, id)
		}
	}
	return listingKey, nil
}

// parseDocFilename parses a document filename and extracts the int32 hash and file extension
// Example: "901700106.pdf" -> (901700106, ".pdf", nil)
// Example: "-1389969486.mp3" -> (-1389969486, ".mp3", nil)
func parseDocFilename(filename string) (int32, string, error) {
	// 分离文件名和扩展名
	ext := filepath.Ext(filename)
	nameWithoutExt := strings.TrimSuffix(filename, ext)

	// 转换为 int32
	hash, err := strconv.ParseInt(nameWithoutExt, 10, 32)
	if err != nil {
		return 0, "", fmt.Errorf("failed to parse filename '%s' as int32: %w", nameWithoutExt, err)
	}

	return int32(hash), ext, nil
}

// processDocLH processes the docLH field and returns a slice of DocInfo
// Handles different array types from MongoDB: []interface{}, primitive.A, []string
func processDocLH(docLH interface{}) ([]DocInfo, error) {
	if docLH == nil {
		return nil, nil
	}

	// Helper function to process a single filename
	processFilename := func(filename string) (DocInfo, error) {
		hash, ext, err := parseDocFilename(filename)
		if err != nil {
			golog.Warn("Failed to parse document filename", "filename", filename, "error", err)
			return DocInfo{}, err
		}
		golog.Debug("Parsed document filename", "filename", filename, "hash", hash, "extension", ext)
		return DocInfo{Hash: hash, Extension: ext}, nil
	}

	// Convert different array types to []string and process
	var filenames []string
	switch docLHArray := docLH.(type) {
	case []interface{}:
		// Handle []interface{} type
		for _, doc := range docLHArray {
			if filename, ok := doc.(string); ok {
				filenames = append(filenames, filename)
			} else {
				golog.Warn("Expected string filename in docLH", "type", fmt.Sprintf("%T", doc), "value", doc)
			}
		}
	case primitive.A:
		// Handle BSON array type (primitive.A)
		for _, doc := range docLHArray {
			if filename, ok := doc.(string); ok {
				filenames = append(filenames, filename)
			} else {
				golog.Warn("Expected string filename in docLH", "type", fmt.Sprintf("%T", doc), "value", doc)
			}
		}
	case []string:
		// Handle []string type directly
		filenames = docLHArray
	default:
		return nil, fmt.Errorf("docLH is not a supported array type, got type: %T", docLH)
	}

	// Process all filenames with unified logic
	var docInfos []DocInfo
	for _, filename := range filenames {
		docInfo, err := processFilename(filename)
		if err != nil {
			continue // Error already logged in processFilename
		}
		docInfos = append(docInfos, docInfo)
	}

	return docInfos, nil
}

// deleteSourceFiles deletes source files after successful database update
// Only deletes files that were successfully migrated (hard links created)
func deleteSourceFiles(renameResults []RenameResult, stats *MigrationStats) error {
	if len(renameResults) == 0 {
		return nil
	}

	golog.Info("Starting deletion of source files after successful database update", "fileCount", len(renameResults))
	deletedCount := 0
	deleteErrors := 0
	var errors []string

	for _, result := range renameResults {
		if !result.Success {
			continue // Skip failed migrations
		}

		// Delete source file
		if err := os.Remove(result.OldPath); err != nil {
			golog.Error("Failed to delete source file",
				"file", result.OldPath,
				"error", err)
			deleteErrors++
			errors = append(errors, fmt.Sprintf("failed to delete %s: %v", result.OldPath, err))
		} else {
			golog.Debug("Successfully deleted source file", "file", result.OldPath)
			deletedCount++
		}
	}

	// Update statistics
	if deletedCount > 0 {
		stats.FilesDeleted += int64(deletedCount)
		speedMeter.Check("sourceFilesDeleted", float64(deletedCount))
	}

	golog.Info("Source file deletion completed",
		"totalFiles", len(renameResults),
		"deletedSuccess", deletedCount,
		"deleteErrors", deleteErrors)

	if len(errors) > 0 {
		return fmt.Errorf("source file deletion errors: %s", strings.Join(errors, "; "))
	}

	return nil
}

// rollbackImageMigration rolls back successfully migrated files by removing hard links
// Since source files are not deleted during migration, we only need to remove the hard links
func rollbackImageMigration(renameResults []RenameResult) {
	if len(renameResults) == 0 {
		return
	}

	golog.Info("Starting rollback of image migration by removing hard links", "fileCount", len(renameResults))
	rollbackCount := 0
	rollbackErrors := 0

	for _, result := range renameResults {
		if !result.Success {
			continue // Skip failed migrations
		}

		// Simply remove the hard link file (source file is still intact)
		if err := os.Remove(result.NewPath); err != nil {
			golog.Error("Failed to remove hard link during rollback",
				"file", result.NewPath,
				"error", err)
			rollbackErrors++
		} else {
			golog.Debug("Successfully removed hard link during rollback",
				"file", result.NewPath)
			rollbackCount++
		}
	}

	golog.Info("Rollback completed",
		"totalFiles", len(renameResults),
		"rollbackSuccess", rollbackCount,
		"rollbackErrors", rollbackErrors)
}

// printFinalStats prints the final migration statistics
func printFinalStats(stats *MigrationStats, duration time.Duration) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("MIGRATION SUMMARY")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("Total Processing Time: %v\n", duration)
	fmt.Printf("Total Records Processed: %d\n", stats.TotalProcessed)
	fmt.Printf("Records Already Correct: %d\n", stats.AlreadyCorrect)
	fmt.Printf("Records Needing Migration: %d\n", stats.NeedsMigration)

	if !*dryrunFlag {
		fmt.Printf("File Migration Success: %d\n", stats.MigrationSuccess)
		fmt.Printf("File Migration Failed: %d\n", stats.MigrationFailed)
		fmt.Printf("Database Updates Success: %d\n", stats.DatabaseUpdated)
		fmt.Printf("Database Updates Failed: %d\n", stats.DatabaseFailed)
		fmt.Printf("Files Not Found: %d\n", stats.FilesNotFound)
		fmt.Printf("Files Deleted: %d\n", stats.FilesDeleted)
		fmt.Printf("Properties Added to Queue: %d\n", stats.AddedToQueue)
		fmt.Printf("Photo Fields Unset: %d\n", stats.FieldsUnset)
	} else {
		fmt.Println("Mode: DRY RUN (no actual changes made)")
	}

	fmt.Println(strings.Repeat("=", 60))

	// Log the same information
	golog.Info("Migration completed",
		"duration", duration,
		"totalProcessed", stats.TotalProcessed,
		"alreadyCorrect", stats.AlreadyCorrect,
		"needsMigration", stats.NeedsMigration,
		"migrationSuccess", stats.MigrationSuccess,
		"migrationFailed", stats.MigrationFailed,
		"databaseUpdated", stats.DatabaseUpdated,
		"databaseFailed", stats.DatabaseFailed,
		"filesNotFound", stats.FilesNotFound,
		"filesDeleted", stats.FilesDeleted,
		"addedToQueue", stats.AddedToQueue,
		"fieldsUnset", stats.FieldsUnset,
		"dryrun", *dryrunFlag)
}

// updateDirStoreStats updates directory statistics after file migration
// oldPath: source directory path (e.g., "/1200/abc12")
// newPath: destination directory path (e.g., "/1274/ffc41")
// fileCount: number of files migrated
func updateDirStoreStats(oldPath, newPath string, fileCount int) error {
	if fileCount <= 0 {
		return nil // No files migrated, no stats to update
	}

	// Parse L1 and L2 from old path
	oldL1, oldL2, err := parseL1L2FromPath(oldPath)
	if err != nil {
		return fmt.Errorf("failed to parse old path %s: %w", oldPath, err)
	}

	// Parse L1 and L2 from new path
	newL1, newL2, err := parseL1L2FromPath(newPath)
	if err != nil {
		return fmt.Errorf("failed to parse new path %s: %w", newPath, err)
	}

	// Update statistics: decrease old directory, increase new directory
	// Entity count is 0 because we're moving files, not adding/removing properties
	dirStore.AddDirStats(oldL1, oldL2, 0, -fileCount) // Decrease file count in old directory
	dirStore.AddDirStats(newL1, newL2, 0, fileCount)  // Increase file count in new directory

	golog.Info("Updated dirStore statistics",
		"oldPath", oldPath,
		"newPath", newPath,
		"oldL1", oldL1,
		"oldL2", oldL2,
		"newL1", newL1,
		"newL2", newL2,
		"fileCount", fileCount)

	return nil
}

// parseL1L2FromPath parses L1 and L2 from a path like "/1200/abc12"
func parseL1L2FromPath(path string) (string, string, error) {
	// Remove leading slash and split by "/"
	path = strings.TrimPrefix(path, "/")
	parts := strings.Split(path, "/")

	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid path format, expected /L1/L2, got: %s", path)
	}

	l1 := parts[0]
	l2 := parts[1]

	if l1 == "" || l2 == "" {
		return "", "", fmt.Errorf("empty L1 or L2 in path: %s", path)
	}

	return l1, l2, nil
}

// handleMissingFiles handles the case when image or document files are missing
// It unsets photo and document related fields and adds the property to download queue
func handleMissingFiles(ctx context.Context, coll *gomongo.MongoCollection, doc bson.M, stats *MigrationStats) error {
	id, ok := doc["_id"].(string)
	if !ok {
		return fmt.Errorf("failed to extract _id from document")
	}

	// Get listing key for queue priority calculation
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get listing key: %w", err)
	}

	golog.Info("Handling missing files - unsetting photo fields and adding to queue",
		"_id", id,
		"listingKey", listingKey,
		"board", *boardFlag)

	// Step 1: Delete existing image files if they exist
	if err := deleteExistingImages(doc, stats); err != nil {
		golog.Warn("Failed to delete existing images", "error", err, "_id", id)
		// Continue with database update even if file deletion fails
	}

	// Step 2: Unset photo and document related fields in database
	unsetUpdate := bson.M{
		"$unset": bson.M{
			"phoP":  "",
			"phoLH": "",
			"tnLH":  "",
			"docLH": "",
		},
	}

	result, err := coll.UpdateOne(ctx, bson.M{"_id": id}, unsetUpdate)
	if err != nil {
		return fmt.Errorf("failed to unset photo and document fields: %w", err)
	}

	if result.ModifiedCount > 0 {
		stats.FieldsUnset++
		speedMeter.Check("fieldsUnset", 1)
		golog.Info("Successfully unset photo and document fields", "_id", id, "modifiedCount", result.ModifiedCount)
	}

	// Step 3: Calculate priority and add to download queue
	priority := getPriorityForMissingFiles(doc)
	if err := queue.AddToQueue(id, priority, *boardFlag); err != nil {
		return fmt.Errorf("failed to add to download queue: %w", err)
	}

	stats.AddedToQueue++
	speedMeter.Check("addedToQueue", 1)
	golog.Info("Successfully added to download queue",
		"_id", id,
		"listingKey", listingKey,
		"priority", priority,
		"board", *boardFlag)

	return nil
}

// deleteExistingImages deletes existing image and document files for a property
func deleteExistingImages(doc bson.M, stats *MigrationStats) error {
	// Extract phoP path
	phoP, ok := doc["phoP"].(string)
	if !ok || phoP == "" {
		golog.Debug("No phoP found, skipping image deletion")
		return nil
	}

	// Extract image hashes
	var imageHashes []string

	// Process phoLH
	if phoLH, ok := doc["phoLH"]; ok && phoLH != nil {
		if phoLHArray, ok := phoLH.([]interface{}); ok {
			for _, hash := range phoLHArray {
				if hashInt32, ok := hash.(int32); ok {
					base62Hash, err := levelStore.Int32ToBase62(hashInt32)
					if err != nil {
						golog.Error("Failed to convert phoLH hash to base62", "hash", hashInt32, "error", err)
						continue
					}
					imageHashes = append(imageHashes, base62Hash)
				}
			}
		}
	}

	// Process tnLH
	if tnLH, ok := doc["tnLH"]; ok && tnLH != nil {
		if tnHashInt32, ok := tnLH.(int32); ok {
			base62Hash, err := levelStore.Int32ToBase62(tnHashInt32)
			if err != nil {
				golog.Error("Failed to convert tnLH hash to base62", "hash", tnHashInt32, "error", err)
			} else {
				imageHashes = append(imageHashes, base62Hash)
			}
		}
	}

	// Process docLH (document files)
	var docInfos []DocInfo
	if docLH, ok := doc["docLH"]; ok && docLH != nil {
		parsedDocInfos, err := processDocLH(docLH)
		if err != nil {
			golog.Error("Failed to process docLH for deletion", "error", err)
		} else {
			docInfos = parsedDocInfos
		}
	}

	// Check if there are any files to delete
	totalFiles := len(imageHashes) + len(docInfos)
	if totalFiles == 0 {
		golog.Debug("No files found for deletion", "phoP", phoP)
		return nil
	}

	// Get listing key for filename generation
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get listing key for file deletion: %w", err)
	}

	// Get storage paths
	storagePaths, err := levelStore.GetImageDir(*boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get image directories: %w", err)
	}

	deletedCount := 0
	for _, storagePath := range storagePaths {
		dirPath := filepath.Join(storagePath, strings.TrimPrefix(phoP, "/"))

		for _, hash := range imageHashes {
			filename := fmt.Sprintf("%s_%s.jpg", listingKey, hash)
			filePath := filepath.Join(dirPath, filename)

			if _, err := os.Stat(filePath); err == nil {
				if err := os.Remove(filePath); err != nil {
					golog.Error("Failed to delete image file", "file", filePath, "error", err)
				} else {
					deletedCount++
					golog.Debug("Successfully deleted image file", "file", filePath)
				}
			}
		}

		// Delete document files
		for _, docInfo := range docInfos {
			// Convert int32 hash to base62 for filename
			base62Hash, err := levelStore.Int32ToBase62(docInfo.Hash)
			if err != nil {
				golog.Error("Failed to convert document hash to base62 for deletion", "hash", docInfo.Hash, "error", err)
				continue
			}

			filename := fmt.Sprintf("%s_%s%s", listingKey, base62Hash, docInfo.Extension)
			filePath := filepath.Join(dirPath, filename)

			if _, err := os.Stat(filePath); err == nil {
				if err := os.Remove(filePath); err != nil {
					golog.Error("Failed to delete document file", "file", filePath, "error", err)
				} else {
					deletedCount++
					golog.Debug("Successfully deleted document file", "file", filePath)
				}
			}
		}
	}

	if deletedCount > 0 {
		stats.FilesDeleted += int64(deletedCount)
		speedMeter.Check("filesDeleted", float64(deletedCount))
		golog.Info("Deleted existing files",
			"count", deletedCount,
			"imageCount", len(imageHashes),
			"documentCount", len(docInfos),
			"phoP", phoP)
	}

	return nil
}

// getPriorityForMissingFiles calculates priority for properties with missing files
func getPriorityForMissingFiles(doc bson.M) int {
	// Use high priority for missing files to ensure they get processed quickly
	// This is similar to the NoPhotoPriorityBonus in the priority calculator
	basePriority := 30000 // High priority for missing files

	// Try to use the standard priority calculator if possible
	if priority, err := goresodownload.CalculatePriority(*boardFlag, doc); err == nil {
		// Add bonus to calculated priority
		return priority + basePriority
	}

	// Fallback to base priority
	return basePriority
}
